# 项目财务处理分析报告

## 概述

本文档详细分析了项目中对资和对客的利息、罚金、手续费、还款计划等财务处理逻辑。

## 1. 费率体系

### 1.1 费率等级 (RateLevel)

项目中定义了两个主要的费率等级：

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/RateLevel.java" mode="EXCERPT">
````java
public enum RateLevel {
    /**
     * 24
     */
    RATE_24(new BigDecimal("0.2399")),

    /**
     * 36
     */
    RATE_36(new BigDecimal("0.3599")),
    /**
     * 咨询费月利率
     */
    CONSULT_FEE_MONTH_RATE_36(new BigDecimal("0.01"));
````
</augment_code_snippet>

- **RATE_24**: 23.99% 年化利率，主要用于权益类客户
- **RATE_36**: 35.99% 年化利率，主要用于普通客户
- **CONSULT_FEE_MONTH_RATE_36**: 1% 月利率，用于咨询费计算

### 1.2 银行费率配置 (QhBank)

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/QhBank.java" mode="EXCERPT">
````java
public enum QhBank {
    CYBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.CYBK, BindSignMode.SHARE, false, "长银消金", 5),
    HXBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.HXBK, BindSignMode.SHARE, false, "湖消", 3),
    ;

    /**
     * bank对客利率
     */
    private final BigDecimal bankCustomRate;
    /**
     * bank合同利率
     */
    private final BigDecimal bankRate;
    /**
     * 宽限期
     */
    private final int graceDay;
````
</augment_code_snippet>

- **bankCustomRate**: 23.99% - 银行对客户的利率
- **bankRate**: 15% - 银行合同利率（对资利率）
- **graceDay**: 宽限期天数（长银5天，湖消3天）

## 2. 还款计划生成

### 2.1 还款计划计算流程

项目采用等额本息的还款方式，主要通过以下步骤生成还款计划：

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/util/CycfcRepayPlanCalc.java" mode="EXCERPT">
````java
public static List<RepayPlanItem> calcConsultPlan(BigDecimal loanAmt, int periods) {
    List<RepayPlanItem> repayPlanItems = calcGuaranteePlan(loanAmt, periods);
    // 平台对客IRR36,等额本息计算月供
    List<RepayPlan> consultPlans = calcBasicPlan(loanAmt, RateLevel.RATE_36.getRate(), periods);
    for (RepayPlanItem planItem : repayPlanItems) {
        // 计算咨询费
        RepayPlan consultPlan = consultPlans.stream().filter(c -> c.getCurrentPeriod() == planItem.getPeriod()).findFirst().orElseThrow();
        planItem.setConsultAmt(consultPlan.getPrincipal().add(consultPlan.getInterest())
            .subtract(planItem.getCapitalTotalAmt()));
        // 平台对客 = 资方对客 + 咨询费
        planItem.setCustomTotalAmt(planItem.getCapitalTotalAmt().add(planItem.getConsultAmt()));
    }
    return repayPlanItems;
}
````
</augment_code_snippet>

### 2.2 融担费计算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/util/CycfcRepayPlanCalc.java" mode="EXCERPT">
````java
/**
 * 融担费还款计划  融担费日利率（ 融担年化/365 ） * 占用天数 * 剩余本金
 * 按日计算  例如：【23.89%（资方对客利率）- 8.4%（资方利息利率）】/ 365(天)  * 占用天数  * 当前期剩余本金
 */
public static List<RepayPlanItem> calcGuaranteePlan(BigDecimal loanAmt, int periods) {
    List<RepayPlanItem> repayPlanItems = new ArrayList<>();
    //bank合同利率
    List<RepayPlan> bankRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankRate(), periods);
    //bank对客利率
    List<RepayPlan> bankCustomRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankCustomRate(), periods);
````
</augment_code_snippet>

## 3. 费用计算详解

### 3.1 咨询费计算

咨询费的计算分为两种情况：

#### 3.1.1 IRR36客户咨询费

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/TrialService.java" mode="EXCERPT">
````java
//计算咨询费
public BigDecimal planConsultFee(BigDecimal interestAmt, BigDecimal remainingPrincipalAmt, long num, int daysInMonth) {
    // 剩余本金 * 占用天数 0.12 /360 四舍五入 两位小数
    return remainingPrincipalAmt.multiply(new BigDecimal(num)).multiply(new BigDecimal("0.12"))
        .divide(new BigDecimal("360"), 2, RoundingMode.HALF_UP);
}
````
</augment_code_snippet>

**计算公式**: 剩余本金 × 占用天数 × 0.12 ÷ 360

#### 3.1.2 IRR24客户咨询费

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/rate/Rate24Service.java" mode="EXCERPT">
````java
@Override
public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, long num) {
    return BigDecimal.ZERO;
}
````
</augment_code_snippet>

**IRR24客户不收取咨询费**

### 3.2 罚息计算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/overdue/RepayPlanOverdueDueBatch.java" mode="EXCERPT">
````java
/**
 * 咨询费罚息费率
 */
public static final BigDecimal CONSULT_OVERDUE_RATE = new BigDecimal("0.0985");

/**
 * 计算对客罚息
 * 罚息金额 = 逾期未还本金*0.0985%*逾期天数
 */
private BigDecimal totalAmtPenaltyAmt(Loan loan, RepayPlan repayPlan, long overDay) {
    //剩余未还本金
    BigDecimal totalAmtBase = AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt());
    if (totalAmtBase.compareTo(BigDecimal.ZERO) <= 0) {
        return BigDecimal.ZERO;
    }
    BigDecimal consultOverdueRate = CONSULT_OVERDUE_RATE;
    return totalAmtBase.multiply(new BigDecimal(overDay)).multiply(consultOverdueRate.divide(PERCENT)).setScale(2, RoundingMode.HALF_UP);
}
````
</augment_code_snippet>

**罚息计算公式**: 逾期未还本金 × 逾期天数 × 0.0985%

### 3.3 违约金计算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/TrialService.java" mode="EXCERPT">
````java
//提前还款违约金
//最后一期，不收取违约金
if (period.equals(order.getApplyPeriods())) {
    trialResultVo.setBreachFee(BigDecimal.ZERO);
} else {
    //剩余本金*3%-提前结清当期实还息费(利息+担保费+咨询服务费）
    BigDecimal bigDecimal = trialResultVo.getPrincipal().multiply(new BigDecimal("0.03"))
        .subtract((trialResultVo.getInterest().add(consultFee)));
    BigDecimal breachFee = bigDecimal.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : bigDecimal;
    trialResultVo.setBreachFee(breachFee.setScale(2, RoundingMode.HALF_UP));
}
````
</augment_code_snippet>

**违约金计算公式**: 剩余本金 × 3% - 当期实还息费（利息+担保费+咨询服务费）

## 4. 宽限期处理

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
//判断是否未逾期，和 不能有减免
boolean isGracePeriod(RepayPlan repayPlan, LocalDate date, Loan loan) {
    Boolean flag = false;
    //逾期天数
    long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
    //宽限期
    int graceDay = QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay();
    if (loan.getBankChannel()== BankChannel.HXBK){
        //宽限期 年结期间，湖消的逾期天数会增加
         graceDay =hxbkOverDueDayAdd==null?QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay():QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay()+hxbkOverDueDayAdd;
    }
    if (overDay <= graceDay || repayPlan.getPlanRepayDate().isAfter(date)) {
        flag = true;
    }
    return flag;
}
````
</augment_code_snippet>

- **长银消金(CYBK)**: 5天宽限期
- **湖消(HXBK)**: 3天宽限期（年结期间可能增加）

## 5. 对资与对客费用结构

### 5.1 对资费用结构
- **本金**: 等额本息计算的本金部分
- **利息**: 按银行合同利率(15%)计算的利息

### 5.2 对客费用结构
- **本金**: 与对资本金相同
- **利息**: 按银行对客利率(23.99%)计算的利息
- **咨询费**: 根据客户等级计算（IRR36客户收取，IRR24客户不收取）
- **罚息**: 逾期时按0.0985%日利率计算
- **违约金**: 提前还款时收取

### 5.3 费用关系
- **平台对客总额 = 资方对客总额 + 咨询费**
- **融担费 = 银行对客利率还款额 - 银行合同利率还款额**

## 6. 核心计算类说明

- **CycfcRepayPlanCalc**: 润楼长银还款计划计算
- **CommonRepayPlanCalc**: 通用还款计划计算
- **CoreRepayPlanCalculator**: 核心还款计划计算器
- **TszRepayPlanCalculator**: 特殊还款计划计算器
- **PlanGenerator**: 等额本息还款计划生成器

## 7. 还款计划生成流程

### 7.1 还款计划服务流程

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/RepayPlanService.java" mode="EXCERPT">
````java
public List<RepayPlan> generateRepayPlan(Loan loan) {
    PlanQueryDto planQueryDto = new PlanQueryDto();
    planQueryDto.setSysLoanId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
    planQueryDto.setLoanId(loan.getLoanNo());
    // 查询core还款计划
    RestResult<PlanDto> restResult = finRepayService.queryPlan(planQueryDto);

    // 资方本金
    repayPlan.setPrincipalAmt(planItemDto.getPrincipalAmt());
    // 资方利息
    repayPlan.setInterestAmt(planItemDto.getInterestAmt());
    //罚息
    repayPlan.setPenaltyAmt(planItemDto.getPenaltyAmt());
    // 融担费
    repayPlan.setGuaranteeAmt(BigDecimal.ZERO);
    // 咨询费
    repayPlan.setConsultFee(calConsultFee(loan, planItemDto, remainingPrincipalAmt,dateNum));
    // 月供 本+利+罚+咨询
    repayPlan.setAmount(repayPlan.getPrincipalAmt().add(repayPlan.getInterestAmt()).add(repayPlan.getPenaltyAmt()).add(repayPlan.getConsultFee()));
}
````
</augment_code_snippet>

### 7.2 等额本息计算原理

项目使用标准的等额本息计算公式：

**月供计算公式**:
```
月供 = 贷款本金 × [月利率 × (1+月利率)^还款月数] ÷ [(1+月利率)^还款月数 - 1]
```

**每月利息计算**:
```
每月利息 = 贷款本金 × 月利率 × [(1+月利率)^还款月数 - (1+月利率)^(还款月序号-1)] ÷ [(1+月利率)^还款月数 - 1]
```

## 8. 试算服务

### 8.1 还款试算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/TrialService.java" mode="EXCERPT">
````java
public TrialResultVo repayTrial(String loanId, RepayPurpose repayPurpose, Integer period, String repayDate) {
    RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loanId, period);
    if (Objects.isNull(repayDate)) {
        repayDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    TrialResultVo trialResultVo = RepayConvert.INSTANCE.toVo(restResult.getData());
    trialResultVo.setPenalty(repayPlan.getPenaltyAmt());
    //延期入账罚息计算
    extensionPenalty(repayDate, repayPlan, trialResultVo);
    trialResultVo.setConsultFee(repayPlan.getConsultFee());
    //如果是权益类客户，取资方罚息
    if(order.getApproveRate() == RateLevel.RATE_24) {
        trialResultVo.setPenalty(restResult.getData().getOverdueFee());
    }
}
````
</augment_code_snippet>

### 8.2 延期罚息重算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/TrialService.java" mode="EXCERPT">
````java
private void extensionPenalty(String repayDate, RepayPlan repayPlan, TrialResultVo trialResultVo) {
    if (!repayDate.equals(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
        && repayPlan.getPenaltyAmt().compareTo(BigDecimal.ZERO) > 0) {
        // 实际还款时间与当前系统日期不一致 罚息重新就算
        BigDecimal totalAmtBase = AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt());
        if (totalAmtBase.compareTo(BigDecimal.ZERO) <= 0) {
            totalAmtBase = BigDecimal.ZERO;
        }
        long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), LocalDate.parse(repayDate.substring(0, 8), DateTimeFormatter.ofPattern("yyyyMMdd")));
        trialResultVo.setPenalty(totalAmtBase.multiply(new BigDecimal(overDay))
            .multiply(CONSULT_OVERDUE_RATE.divide(PERCENT)).setScale(2, RoundingMode.HALF_UP));
    }
}
````
</augment_code_snippet>

## 9. 缩期处理

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/RepayPlanService.java" mode="EXCERPT">
````java
/**
 * 应还金额 缩期处理
 *
 * @param repayPlan 当期还款计划
 * @return 缩期后的还款计划
 */
public RepayPlan reducePeriod(RepayPlan repayPlan) {
    if (!judgementReducePeriod(repayPlan)) {
        //本期不需要缩期，直接返回null，由各流量做后续处理
        return null;
    }
    RepayPlan newPlan = RepayConvert.INSTANCE.copy(repayPlan);
    //应还本金
    BigDecimal principal = AmountUtil.safeAmount(repayPlan.getPrincipalAmt());
    //应还利息 = 按日计息 = 实际还成功的利息
    BigDecimal interest = AmountUtil.safeAmount(repayPlan.getActInterestAmt());
    //应还罚息(逾期违约金）
    BigDecimal penalty = AmountUtil.safeAmount(repayPlan.getActPenaltyAmt());
    //应还融担费
    BigDecimal guaranteeFee = AmountUtil.safeAmount(repayPlan.getActGuaranteeAmt());
    //应还咨询费(本期+之后所有的咨询费)
    BigDecimal consultFee = AmountUtil.safeAmount(repayPlan.getConsultFee());
}
````
</augment_code_snippet>

## 10. 逾期处理批处理

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/overdue/RepayPlanOverdueDueBatch.java" mode="EXCERPT">
````java
/**
 * 还款计划逾期处理
 */
@Component
@JobHandler("repayPlanOverdueDueBatch")
public class RepayPlanOverdueDueBatch extends AbstractJobHandler {

    /**
     * 咨询费罚息费率
     */
    public static final BigDecimal CONSULT_OVERDUE_RATE = new BigDecimal("0.0985");

    /**
     * 罚息费率为：23.99%/360的资方，咨询费罚息费率
     */
    public static final BigDecimal SPECIAL_CONSULT_OVERDUE_RATE = new BigDecimal("0.067");

    //长银逾期
    if (loan.getBankChannel() == BankChannel.CYBK) {
        for (int i = 0; i < loanRepayPlanList.size(); i++) {
            RepayPlan repayPlan = loanRepayPlanList.get(i);
            //逾期天数
            long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
            if (i == 0) {
                //宽限期
                int graceDay = QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay();
                if (overDay <= graceDay) {
                    continue;
                }
            }
            //应还罚息
            BigDecimal penaltyAmt = totalAmtPenaltyAmt(loan, repayPlan, overDay);
        }
    }
}
````
</augment_code_snippet>

## 11. 银行差异化处理

### 11.1 长银消金(CYBK)特殊处理

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/recc/CYBKReccAbstractHandler.java" mode="EXCERPT">
````java
// 罚息日利率
BigDecimal penaltyRateDay = loan.getBankRate().divide(new BigDecimal("360"), SIX, RoundingMode.HALF_UP);
// 对资罚息：资方罚息 = 逾期金额(当期本金) * 罚息日利率(0.15/360) * 逾期天数
repayPenalty = unpaidPrincipal.multiply(penaltyRateDay).multiply(new BigDecimal(overdueDays))
    .setScale(TWO, RoundingMode.HALF_UP);
````
</augment_code_snippet>

**长银消金罚息计算**: 逾期金额 × 罚息日利率(0.15/360) × 逾期天数

### 11.2 湖消(HXBK)特殊处理

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKRepayService.java" mode="EXCERPT">
````java
LocalDate actualRepayDate = LocalDate.parse(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(mockService.getServerDateTime(repayDate,BankChannel.HXBK)));//实际还款日期
//获取两天差值(逾期天数)
long overDay = DateUtil.dateDiff(curPlan.getRepayDate(), actualRepayDate);
if (overDay <= 3 + hxbkOverDueDayAdd) {
    //说明在宽限期内，传2
    repayType = "2";//还款类型(1: 全部结清；2：正常还款 3：当期结清 4：逾期还款)
    typeLogMsg = "当期";
}
````
</augment_code_snippet>

**湖消宽限期处理**: 基础宽限期3天 + 可配置的额外宽限期天数(hxbkOverDueDayAdd)

### 11.3 银行间费率对比

| 银行 | 对客利率 | 合同利率 | 宽限期 | 罚息计算方式 |
|------|----------|----------|--------|--------------|
| 长银消金(CYBK) | 23.99% | 15% | 5天 | 逾期金额×0.15/360×逾期天数 |
| 湖消(HXBK) | 23.99% | 15% | 3天(可调整) | 按统一罚息费率计算 |

## 12. 融担费计算详解

### 12.1 融担费计算原理

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKLoanService.java" mode="EXCERPT">
````java
/**
 * 每一期担保费：对客利率的等额本息还款金额-8.5%的等额本息还款金额；
 */
public CYBKLoanGuaranteeInfo getGuaranteeAmtAndRate(CYBKLoanGuaranteeInfo guaranteeInfo, Credit credit) {
    //计算 对客利率的等额本息还款金额
    List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
        credit.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());
    BigDecimal totalAmt = repayPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);

    List<RepayPlan> repayBankPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
        credit.getLoanAmt(), credit.getBankRate(), credit.getPeriods());
    BigDecimal totalBankAmt = repayBankPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal guaranteeAmt = totalAmt.subtract(totalBankAmt); //担保费
    guaranteeInfo.setGuarAmt(guaranteeAmt);

    //担保总金额(12期) = 担保费总额 ÷ 实际期数 × 12
    guaranteeAmt = guaranteeAmt.divide(new BigDecimal(credit.getPeriods()), SIX, RoundingMode.HALF_UP).multiply(new BigDecimal(TWELVE));
}
````
</augment_code_snippet>

**融担费计算公式**: 对客利率等额本息总额 - 银行合同利率等额本息总额

### 12.2 按日计算融担费

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/util/CycfcRepayPlanCalc.java" mode="EXCERPT">
````java
/**
 * 融担费还款计划  融担费日利率（ 融担年化/365 ） * 占用天数 * 剩余本金
 * 按日计算  例如：【23.89%（资方对客利率）- 8.4%（资方利息利率）】/ 365(天)  * 占用天数  * 当前期剩余本金
 */
````
</augment_code_snippet>

**按日融担费公式**: (对客利率 - 合同利率) ÷ 365 × 占用天数 × 剩余本金

## 13. 代偿处理机制

### 13.1 长银代偿处理

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKRepayService.java" mode="EXCERPT">
````java
if (originPlan.getBankRepayStatus() == RepayStatus.REPAID) {
    // 对资已代偿,不需要接口通知,直接返回成功
    getWarningService().warn("长银直连 代偿后不需要通知资方 customerRepayRecordId:" + custRepayRecordId);
    result = new RepayResultVo();
    result.setStatus(ProcessStatus.FAIL);
    return result;
}
````
</augment_code_snippet>

### 13.2 湖消代偿处理

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKRepayService.java" mode="EXCERPT">
````java
//判断对资还款状态是否为已代偿
if (Objects.equals(originPlan.getBankRepayStatus(),RepayStatus.REPAID)) {
    // 对资已代偿,不需要接口通知,直接返回成功
    getWarningService().warn("湖消直连 代偿后不需要通知资方 customerRepayRecordId:" + custRepayRecordId);
    result = new RepayResultVo();
    result.setStatus(ProcessStatus.FAIL);
    return result;
}
````
</augment_code_snippet>

**代偿处理逻辑**: 当对资还款状态为已代偿时，不再通知资方，直接返回处理结果

## 14. 数据结构详解

### 14.1 还款计划实体类 (RepayPlan)

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/entity/RepayPlan.java" mode="EXCERPT">
````java
@Entity
@Table(name = "repay_plan")
public class RepayPlan extends BaseEntity {
    private String userId;
    private String loanId;           // 借据id
    private Integer period;          // 期次
    private LocalDate planRepayDate; // 计划还款日
    private BigDecimal principalAmt; // 应还本金
    private BigDecimal interestAmt;  // 应还利息（银行）
    private BigDecimal guaranteeAmt; // 应还担保费
    private BigDecimal penaltyAmt;   // 应还罚息
    private BigDecimal amount;       // 应还总金额
    private BigDecimal consultFee;   // 应还咨询费
    @Enumerated(EnumType.STRING)
    private RepayState custRepayState; // 状态
    private LocalDateTime actRepayTime; // 实还时间
    private BigDecimal actPrincipalAmt; // 实还本金
    private BigDecimal actInterestAmt;  // 实还利息（银行）
}
````
</augment_code_snippet>

### 14.2 还款计划DTO (PlanItemDto)

<augment_code_snippet path="src/main/java/com/jinghang/capital/api/dto/repay/PlanItemDto.java" mode="EXCERPT">
````java
public class PlanItemDto {
    private Integer period;              // 期次
    private LocalDate repayDate;         // 应还时间
    private BigDecimal totalAmt;         // 应还总金额
    private BigDecimal principalAmt;     // 应还本金
    private BigDecimal interestAmt;      // 应还利息
    private BigDecimal penaltyAmt;       // 应还罚息
    private BigDecimal breachAmt;        // 应还违约金
    private BigDecimal guaranteeAmt;     // 融担费用
    private BigDecimal consultAmt;       // 咨询费用
    private BigDecimal guaranteePenaltyAmt; // 融担罚息费用
    private BigDecimal consultPenaltyAmt;   // 咨询罚息费用
    private RepayStatus custRepayStatus;    // 对客还款状态
}
````
</augment_code_snippet>

### 14.3 还款计划项 (RepayPlanItem)

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/util/RepayPlanItem.java" mode="EXCERPT">
````java
/**
 * 360按日记息还款计划
 */
public class RepayPlanItem {
    private Integer period;
    private LocalDate capitalRepayDate;  // 对资还款日期
    private LocalDate customRepayDate;   // 对客还款日期
    private BigDecimal capitalTotalAmt = BigDecimal.ZERO;  // 对资总金额
    private BigDecimal customTotalAmt = BigDecimal.ZERO;   // 对客总金额
    private BigDecimal principalAmt = BigDecimal.ZERO;     // 本金
    private BigDecimal interestAmt = BigDecimal.ZERO;      // 利息
    private BigDecimal guaranteeAmt = BigDecimal.ZERO;     // 融担费
    private BigDecimal consultAmt = BigDecimal.ZERO;       // 咨询费
}
````
</augment_code_snippet>

### 14.4 字段含义说明

| 字段名 | 含义 | 计算方式 |
|--------|------|----------|
| principalAmt | 应还本金 | 等额本息计算的本金部分 |
| interestAmt | 应还利息 | 按银行合同利率计算的利息 |
| guaranteeAmt | 融担费 | (对客利率-合同利率)×剩余本金×天数÷360 |
| consultFee | 咨询费 | IRR36客户按12%年化计算，IRR24客户为0 |
| penaltyAmt | 罚息 | 逾期本金×0.0985%×逾期天数 |
| breachAmt | 违约金 | 剩余本金×3%-当期息费 |
| amount | 应还总额 | 本金+利息+罚息+咨询费 |
| capitalTotalAmt | 对资总额 | 本金+利息（按银行合同利率） |
| customTotalAmt | 对客总额 | 对资总额+咨询费 |

### 14.5 还款状态枚举

- **NORMAL**: 正常待还
- **OVERDUE**: 逾期
- **REPAID**: 已还清
- **PARTIAL**: 部分还款

## 15. 计算示例

### 15.1 等额本息计算示例

假设贷款条件：
- **贷款金额**: 10,000元
- **贷款期数**: 12期
- **银行合同利率**: 15% (年化)
- **银行对客利率**: 23.99% (年化)
- **客户等级**: IRR36

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/util/TszRepayPlanCalculator.java" mode="EXCERPT">
````java
/**
 * 等额本息360按日计息计算公式
 */
// 月利率
final BigDecimal loanMonthRate = loanRate.divide(new BigDecimal(MONTH), RATE_SCALE, RoundingMode.HALF_UP);
// 每月本息金额 = (本金×月利率×(1＋月利率)＾还款月数)÷ ((1＋月利率)＾还款月数-1)
BigDecimal n = loanMonthRate.add(BigDecimal.ONE).pow(periods); //(1＋月利率)＾还款月数
BigDecimal monthIncome = loanAmt.multiply(loanMonthRate).multiply(n)
        .divide(n.subtract(BigDecimal.ONE), MONEY_SCALE, RoundingMode.HALF_UP);

// 每月应还利息=贷款本金×月利率×〔(1+月利率)^还款月数-(1+月利率)^(还款月序号-1)〕÷〔(1+月利率)^还款月数-1〕
BigDecimal monthInterest = loanAmt.multiply(loanMonthRate).multiply(
                loanMonthRate.add(BigDecimal.ONE).pow(periods).subtract(loanMonthRate.add(BigDecimal.ONE).pow(i - 1)))
        .divide(loanMonthRate.add(BigDecimal.ONE).pow(periods).subtract(BigDecimal.ONE), MONEY_SCALE, RoundingMode.HALF_UP);
````
</augment_code_snippet>

### 15.2 具体计算过程

#### 15.2.1 对资计算（银行合同利率15%）
- **月利率**: 15% ÷ 12 = 1.25%
- **月供**: 10,000 × [1.25% × (1+1.25%)^12] ÷ [(1+1.25%)^12 - 1] ≈ 888.49元

#### 15.2.2 对客计算（银行对客利率23.99%）
- **月利率**: 23.99% ÷ 12 = 1.9992%
- **月供**: 10,000 × [1.9992% × (1+1.9992%)^12] ÷ [(1+1.9992%)^12 - 1] ≈ 926.58元

#### 15.2.3 咨询费计算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/TrialService.java" mode="EXCERPT">
````java
//计算咨询费
public BigDecimal planConsultFee(BigDecimal interestAmt, BigDecimal remainingPrincipalAmt, long num, int daysInMonth) {
    // 剩余本金 * 占用天数 0.12 /360 四舍五入 两位小数
    return remainingPrincipalAmt.multiply(new BigDecimal(num)).multiply(new BigDecimal("0.12"))
        .divide(new BigDecimal("360"), 2, RoundingMode.HALF_UP);
}
````
</augment_code_snippet>

**第一期咨询费计算**:
- 剩余本金: 10,000元
- 占用天数: 30天（假设）
- 咨询费: 10,000 × 30 × 0.12 ÷ 360 = 100元

#### 15.2.4 融担费计算

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/util/CycfcRepayPlanCalc.java" mode="EXCERPT">
````java
/**
 * 融担费还款计划  融担费日利率（ 融担年化/365 ） * 占用天数 * 剩余本金
 * 按日计算  例如：【23.89%（资方对客利率）- 8.4%（资方利息利率）】/ 365(天)  * 占用天数  * 当前期剩余本金
 */
````
</augment_code_snippet>

**融担费计算**:
- 融担费率: (23.99% - 15%) ÷ 365 = 0.0246%（日利率）
- 第一期融担费: 10,000 × 30 × 0.0246% = 73.8元

### 15.3 第一期还款计划汇总

| 费用项目 | 对资金额 | 对客金额 | 计算说明 |
|----------|----------|----------|----------|
| 本金 | 763.49元 | 763.49元 | 等额本息计算的本金部分 |
| 利息 | 125.00元 | 125.00元 | 按银行合同利率15%计算 |
| 融担费 | 0元 | 73.8元 | (23.99%-15%)÷365×30×10000 |
| 咨询费 | 0元 | 100元 | IRR36客户按12%年化计算 |
| **月供总计** | **888.49元** | **1062.29元** | 本金+利息+融担费+咨询费 |

### 15.4 逾期费用计算示例

假设第一期逾期10天：

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/overdue/RepayPlanOverdueDueBatch.java" mode="EXCERPT">
````java
/**
 * 计算对客罚息
 * 罚息金额 = 逾期未还本金*0.0985%*逾期天数
 */
private BigDecimal totalAmtPenaltyAmt(Loan loan, RepayPlan repayPlan, long overDay) {
    //剩余未还本金
    BigDecimal totalAmtBase = AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt());
    BigDecimal consultOverdueRate = CONSULT_OVERDUE_RATE;
    return totalAmtBase.multiply(new BigDecimal(overDay)).multiply(consultOverdueRate.divide(PERCENT)).setScale(2, RoundingMode.HALF_UP);
}
````
</augment_code_snippet>

**罚息计算**:
- 逾期本金: 763.49元
- 逾期天数: 10天
- 罚息: 763.49 × 10 × 0.0985% = 7.52元

### 15.5 提前还款违约金示例

假设第6期提前结清，剩余本金5,000元：

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/TrialService.java" mode="EXCERPT">
````java
//剩余本金*3%-提前结清当期实还息费(利息+担保费+咨询服务费）
BigDecimal bigDecimal = trialResultVo.getPrincipal().multiply(new BigDecimal("0.03"))
    .subtract((trialResultVo.getInterest().add(consultFee)));
BigDecimal breachFee = bigDecimal.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : bigDecimal;
````
</augment_code_snippet>

**违约金计算**:
- 剩余本金: 5,000元
- 违约金基数: 5,000 × 3% = 150元
- 当期息费: 利息62.5元 + 咨询费50元 = 112.5元
- 违约金: 150 - 112.5 = 37.5元

## 16. 总结

### 16.1 系统架构特点

项目的财务处理采用了分层设计：

1. **对资层面**: 使用银行合同利率进行计算
2. **对客层面**: 使用银行对客利率，并根据客户等级收取咨询费
3. **风险控制**: 通过宽限期、罚息、违约金等机制控制风险
4. **灵活配置**: 通过枚举和配置类实现不同银行和费率的灵活管理
5. **批处理机制**: 定时处理逾期账单，自动计算罚息
6. **试算服务**: 提供还款试算功能，支持延期罚息重算
7. **缩期处理**: 支持提前还款的缩期处理逻辑
8. **银行差异化**: 不同银行有不同的宽限期、罚息计算方式
9. **代偿机制**: 完善的代偿处理逻辑，避免重复通知资方

### 16.2 核心计算公式汇总

| 费用类型 | 计算公式 | 适用场景 |
|----------|----------|----------|
| 等额本息月供 | 本金×[月利率×(1+月利率)^期数]÷[(1+月利率)^期数-1] | 所有还款计划 |
| 咨询费(IRR36) | 剩余本金×占用天数×0.12÷360 | IRR36客户 |
| 咨询费(IRR24) | 0 | IRR24权益客户 |
| 融担费 | (对客利率-合同利率)÷365×占用天数×剩余本金 | 按日计算 |
| 罚息 | 逾期本金×0.0985%×逾期天数 | 逾期处理 |
| 违约金 | 剩余本金×3%-当期息费 | 提前还款 |

### 16.3 业务流程要点

1. **还款计划生成**: 先生成对资计划，再计算咨询费得到对客计划
2. **费用分离**: 明确区分对资费用和对客费用，便于资金清算
3. **宽限期处理**: 不同银行有不同的宽限期设置，支持动态调整
4. **逾期处理**: 自动化批处理逾期账单，实时更新罚息
5. **试算功能**: 支持多种还款场景的试算，提供准确的费用预估

### 16.4 技术实现亮点

1. **策略模式**: 通过RateManager和BankManager实现不同费率和银行的策略化处理
2. **精确计算**: 使用BigDecimal确保金融计算的精度
3. **配置化管理**: 通过枚举类集中管理费率和银行配置
4. **异常处理**: 完善的异常处理机制，确保系统稳定性
5. **数据一致性**: 通过事务管理确保财务数据的一致性

整个系统确保了对资方和客户的费用计算准确性，同时提供了完善的逾期处理和风险控制机制，为金融业务提供了可靠的技术支撑。
